information: Include file: <stdbool.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\I2C_communication.h(35)
information: Include file: <stdint.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\I2C_communication.h(36)
information: Include file: <math.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(34)
style: Boolean result is used in bitwise operation. Clarify expression with parentheses. [clarifyCondition]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(217)
style: Boolean result is used in bitwise operation. Clarify expression with parentheses. [clarifyCondition]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(251)
style: The scope of the variable 'VTEMP' can be reduced. [variableScope]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(454)
style: Local variable 'VTEMP' shadows outer variable [shadowVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(433)
style: Local variable 'VTEMP' shadows outer variable [shadowVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(454)
style: Parameter 'THRM_Coefficients' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(431)
style: Variable 'a' can be declared as const array [constVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(716)
style: Variable 'b' can be declared as const array [constVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(717)
warning: Uninitialized variable: VTEMPfiltered [uninitvar]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(467)
error: Uninitialized variable: input_n_minus_2 [uninitvar]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(726)
error: Uninitialized variable: input_n_minus_1 [uninitvar]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(727)
error: Uninitialized variable: Result_filter_n_minus_2 [uninitvar]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(728)
error: Uninitialized variable: Result_filter_n_minus_1 [uninitvar]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(729)
style: Variable 'VTEMP' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(454)
style: Variable 'res_l' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(519)
style: Variable 'res_h' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(520)
style: Variable 'diff_l' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(521)
style: Variable 'diff_h' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(523)
style: Variable 'Result_filter_n_minus_2' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(731)
style: Variable 'Result_filter_n_minus_1' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(732)
style: Variable 'input_n_minus_2' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(733)
style: Variable 'input_n_minus_1' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(734)
style: Parameter 'ptr' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(130)
style: Parameter 'ptr' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(143)
style: Variable 'sum' is assigned a value that is never used. [unreadVariable]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(636)
error: Array 'CANDLC_Coding[16][2]' accessed at index CANDLC_Coding[16][1], which is out of bounds. [arrayIndexOutOfBounds]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(101)
error: Array 'CANDLC_Coding[16][2]' accessed at index CANDLC_Coding[16][1], which is out of bounds. [arrayIndexOutOfBounds]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(293)
style: The scope of the variable 'idMode' can be reduced. [variableScope]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(152)
style: The scope of the variable 'id' can be reduced. [variableScope]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(153)
style: The scope of the variable 'idMode' can be reduced. [variableScope]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(340)
style: The scope of the variable 'id' can be reduced. [variableScope]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(341)
style: Parameter 'data' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(74)
style: Parameter 'data' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(119)
style: Parameter 'data' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(266)
style: Parameter 'data' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(311)
style: Parameter 'source' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\I2C_communication.c(50)
style: Parameter 'reg_data' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\I2C_communication.c(59)
style: Parameter 'data' can be declared as pointer to const [constParameterPointer]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\UART_communication.c(46)
information: Include file: <ti/devices/msp/msp.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\ti_msp_dl_config.h(54)
information: Include file: <ti/driverlib/driverlib.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\ti_msp_dl_config.h(55)
information: Include file: <ti/driverlib/m0p/dl_core.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\ti_msp_dl_config.h(56)
style: The function 'BMU_Latch_Mode' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(269)
style: The function 'Temp_Mux_Polling' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(383)
style: The function 'BMU_FET_Test' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(400)
style: The function 'TMP61_Offset_Calibration' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(451)
style: The function 'TMP61_Averaging_and_Filtering' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(487)
style: The function 'Lookup_THRM_L' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(509)
style: The function 'IIR_Filtering' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\App_task.c(713)
style: The function 'BQ769x2_ReadPFStatus' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(327)
style: The function 'BQ769x2_ReadFETStatus' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(345)
style: The function 'BQ769x2_ReadMANUFACTURINGStatus' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(351)
style: The function 'BQ769x2_Readpullup_pad_RES' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(400)
style: The function 'BQ769x2_Reset' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(409)
style: The function 'BQ769x2_ReadVoltage' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(418)
style: The function 'BQ769x2_ReadTemperature' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(457)
style: The function 'BQ769x2_EnableAllFETs' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(483)
style: The function 'BQ769x2_OTP_Programming' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(497)
style: The function 'BQ769x2_Current_BoardOffset_Calibration' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(600)
style: The function 'BQ769x2_Current_Gain_Calibration' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\BQ769x2_Configs\BQ769x2_protocol.c(630)
style: The function 'MCAN0_INST_IRQHandler' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(218)
style: The function 'CAN1TxMsgSendParamInitDefault' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(253)
style: The function 'CAN1_Write' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(310)
style: The function 'CAN1processCANRxMsg' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(338)
style: The function 'MCAN1_INST_IRQHandler' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\CAN_communication.c(381)
style: The function 'CopyArray' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\I2C_communication.c(50)
style: The function 'I2C_0_INST_IRQHandler' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\I2C_communication.c(135)
style: The function 'UART_0_INST_IRQHandler' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Communications\UART_communication.c(131)
style: The function 'SYSCFG_DL_saveConfiguration' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\ti_msp_dl_config.c(75)
style: The function 'SYSCFG_DL_restoreConfiguration' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\ti_msp_dl_config.c(86)
style: The function 'TIMER_0_INST_IRQHandler' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\main.c(108)
style: The function 'GROUP1_IRQHandler' is never used. [unusedFunction]
    C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\main.c(125)
information: Active checkers: 110/960 (use --checkers-report=<filename> to see details) [checkersReport]
    nofile(0)
