@echo off
PATH=%PATH%;C:\Program Files\Cppcheck
set PROJECT_PATH=C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.clangd
cd %PROJECT_PATH%

REM Run cppcheck with all checks enabled, focusing on security issues
cppcheck --project=.\compile_commands.json ^
    --enable=all ^
    --enable=warning,style,performance,portability,information ^
    --std=c11 ^
    --suppress=missingInclude ^
    --template="{severity}: {message} [{id}]\n    {file}({line})" ^
    --output-file=C:\NXP\cppcheck-result.txt
cd C:\NXP

REM Check if cppcheck execution was successful
if %errorlevel% neq 0 (
    echo Cppcheck execution failed. Please verify cppcheck installation.
    exit /b 1
)
REM Check for error-level issues (including security errors)
findstr /I /C:"error:" cppcheck-result.txt > nul
if %errorlevel% equ 0 (
    echo [ERROR] Critical or security issues found!
    echo.
    REM Display only error-level issues
    findstr /I /C:"error:" cppcheck-result.txt
    echo.
    echo For complete analysis report, please check cppcheck-result.txt
    exit /b 1
) else (
    echo [PASS] Check completed. No critical errors found.
    echo.
    echo Warnings and other suggestions:
    type cppcheck-result.txt
    exit /b 0
)
