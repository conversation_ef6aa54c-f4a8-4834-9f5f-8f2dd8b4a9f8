@echo off
PATH=%PATH%;C:\Program Files\Cppcheck
set PROJECT_PATH=C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.clangd
cd %PROJECT_PATH%

REM Run cppcheck with clang integration and all checks enabled
cppcheck --project=.\compile_commands.json ^
    --enable=all ^
    --enable=warning,style,performance,portability,information ^
    --std=c11 ^
    --suppress=missingInclude ^
    --suppress=missingIncludeSystem ^
    --clang ^
    --clang-tidy ^
    --template="{severity}: {message} [{id}]\n    {file}({line})" ^
    --output-file=C:\NXP\cppcheck-result.txt
cd C:\NXP

REM Check if cppcheck with clang execution was successful
if %errorlevel% neq 0 (
    echo Cppcheck with clang integration failed. Trying fallback without clang...
    echo This might be due to:
    echo 1. clang not found in PATH
    echo 2. Invalid compile_commands.json
    echo 3. Missing include paths
    echo.

    REM Fallback to standard cppcheck without clang
    cd %PROJECT_PATH%
    cppcheck --project=.\compile_commands.json ^
        --enable=all ^
        --enable=warning,style,performance,portability,information ^
        --std=c11 ^
        --suppress=missingInclude ^
        --template="{severity}: {message} [{id}]\n    {file}({line})" ^
        --output-file=C:\NXP\cppcheck-result.txt

    cd C:\NXP
    if %errorlevel% neq 0 (
        echo Standard cppcheck also failed. Please verify cppcheck installation.
        exit /b 1
    ) else (
        echo Fallback to standard cppcheck completed successfully.
    )
)
REM Check for error-level issues (including security errors)
findstr /I /C:"error:" cppcheck-result.txt > nul
if %errorlevel% equ 0 (
    echo [ERROR] Critical or security issues found!
    echo.
    REM Display only error-level issues
    findstr /I /C:"error:" cppcheck-result.txt
    echo.
    echo For complete analysis report, please check cppcheck-result.txt
    exit /b 1
) else (
    echo [PASS] Check completed. No critical errors found.
    echo.
    echo Warnings and other suggestions:
    type cppcheck-result.txt
    exit /b 0
)
