---
# Clang-tidy configuration for embedded C projects
Checks: >
  -*,
  bugprone-*,
  cert-*,
  clang-analyzer-*,
  concurrency-*,
  cppcoreguidelines-*,
  hicpp-*,
  misc-*,
  modernize-*,
  performance-*,
  portability-*,
  readability-*,
  security-*,
  -bugprone-easily-swappable-parameters,
  -bugprone-implicit-widening-of-multiplication-result,
  -cert-dcl03-c,
  -cert-dcl37-c,
  -cert-dcl51-cpp,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-init-variables,
  -hicpp-signed-bitwise,
  -misc-unused-parameters,
  -modernize-macro-to-enum,
  -readability-function-cognitive-complexity,
  -readability-identifier-length,
  -readability-magic-numbers

WarningsAsErrors: ''
HeaderFilterRegex: '.*'
FormatStyle: file
CheckOptions:
  - key: readability-identifier-naming.VariableCase
    value: lower_case
  - key: readability-identifier-naming.FunctionCase
    value: lower_case
  - key: readability-identifier-naming.MacroCase
    value: UPPER_CASE
  - key: readability-identifier-naming.ConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.EnumCase
    value: lower_case
  - key: readability-identifier-naming.StructCase
    value: lower_case
  - key: performance-unnecessary-value-param.AllowedTypes
    value: 'std::function;std::unique_ptr;std::shared_ptr'
