@echo off
REM Enhanced Cppcheck with clang integration
PATH=%PATH%;C:\Program Files\Cppcheck
set PROJECT_PATH=C:\Users\<USER>\workspace_ccstheia\bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang\Debug\.clangd
set TI_CLANG_PATH=C:\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\bin

REM Add TI clang to PATH if available
if exist "%TI_CLANG_PATH%" (
    PATH=%PATH%;%TI_CLANG_PATH%
    echo Using TI clang from: %TI_CLANG_PATH%
) else (
    echo Warning: TI clang not found at %TI_CLANG_PATH%
)

cd %PROJECT_PATH%

echo Running Cppcheck with clang integration...
echo Project path: %PROJECT_PATH%
echo.

REM Run cppcheck with clang integration
cppcheck --project=.\compile_commands.json ^
    --enable=all ^
    --enable=warning,style,performance,portability,information ^
    --std=c11 ^
    --suppress=missingInclude ^
    --suppress=missingIncludeSystem ^
    --clang ^
    --clang-tidy ^
    --max-ctu-depth=10 ^
    --check-level=exhaustive ^
    --template="{severity}: {message} [{id}]\n    {file}({line})" ^
    --output-file=C:\NXP\cppcheck-clang-result.txt

cd C:\NXP

REM Check if cppcheck execution was successful
if %errorlevel% neq 0 (
    echo Cppcheck with clang integration failed. 
    echo This might be due to:
    echo 1. clang not found in PATH
    echo 2. Invalid compile_commands.json
    echo 3. Missing include paths
    echo.
    echo Falling back to standard cppcheck...
    cd %PROJECT_PATH%
    cppcheck --project=.\compile_commands.json ^
        --enable=all ^
        --std=c11 ^
        --suppress=missingInclude ^
        --template="{severity}: {message} [{id}]\n    {file}({line})" ^
        --output-file=C:\NXP\cppcheck-fallback-result.txt
    cd C:\NXP
    set RESULT_FILE=cppcheck-fallback-result.txt
) else (
    set RESULT_FILE=cppcheck-clang-result.txt
)

REM Check for error-level issues
findstr /I /C:"error:" %RESULT_FILE% > nul
if %errorlevel% equ 0 (
    echo [ERROR] Critical or security issues found!
    echo.
    REM Display only error-level issues
    findstr /I /C:"error:" %RESULT_FILE%
    echo.
    echo For complete analysis report, please check %RESULT_FILE%
    exit /b 1
) else (
    echo [PASS] Check completed. No critical errors found.
    echo.
    echo Warnings and other suggestions:
    type %RESULT_FILE%
    exit /b 0
)
