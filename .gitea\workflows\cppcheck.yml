name: Static Code Analysis

on:
  push:
    branches:
      - main
      - develope
  pull_request:
    branches:
      - main
      - master

jobs:
  cppcheck:
    runs-on: windows-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup project path
        run: |
          set PROJECT_PATH=${{ github.workspace }}\Debug\.clangd
          if not exist "%PROJECT_PATH%" mkdir "%PROJECT_PATH%"
        shell: cmd

      - name: Run Cppcheck Analysis
        run: |
          cd "%PROJECT_PATH%"
          rem Run cppcheck with XML output for HTML report generation
          cppcheck --project=.\compile_commands.json ^
            --enable=all ^
            --enable=warning,style,performance,portability,information ^
            --std=c11 ^
            --suppress=missingInclude ^
            --xml ^
            --xml-version=2 ^
            --output-file=${{ github.workspace }}/cppcheck-result.xml 2>&1
        shell: cmd
        continue-on-error: true

      - name: Check for Errors
        run: |
          findstr /C:"severity=\"error\"" "${{ github.workspace }}/cppcheck-result.xml" >nul
          if %errorlevel% equ 0 (
            echo [ERROR] Critical or security issues found!
            echo.
            findstr /C:"severity=\"error\"" "${{ github.workspace }}/cppcheck-result.xml"
            echo.
            echo For complete analysis report, please check the HTML report
            exit /b 1
          ) else (
            echo [PASS] Check completed. No critical errors found.
            echo.
            echo Analysis complete. Check the HTML report for detailed results.
          )
        shell: cmd

      - name: Convert XML to HTML
        run: |
          cppcheck-htmlreport --file=${{ github.workspace }}/cppcheck-result.xml --report-dir=${{ github.workspace }}/cppcheck-html-report --source-dir=${{ github.workspace }}
        shell: cmd
        if: always()

      - name: Upload Analysis Results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cppcheck-results
          path: |
            ${{ github.workspace }}/cppcheck-result.xml
            ${{ github.workspace }}/cppcheck-html-report/
